import { BackHandler, Platform } from 'react-native';
import { router } from 'expo-router';

export const setupBackHandler = (handleLogout: () => void) => {
  const handleBackPress = () => {
    if (router.canGoBack()) {
      return false; 
    }
    handleLogout();
    return true;
  };

  if (Platform.OS === 'android') {
    const subscription = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    return () => subscription.remove();
  }
}; 