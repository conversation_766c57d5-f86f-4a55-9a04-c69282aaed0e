import React, { useEffect, useCallback, useState, useRef } from 'react';
import { SplashScreen, Stack, useRouter, useSegments, usePathname, useNavigation } from 'expo-router';
import { useAuth } from '../hooks/useAuth';
import { useUserStore } from '../store/useUserStore';
import { PaperProvider, adaptNavigationTheme, MD3DarkTheme, MD3LightTheme } from 'react-native-paper';
import { NetworkStatus } from '../components/common/NetworkStatus';
import { View, Animated, Easing, Text, useColorScheme, Platform } from 'react-native';
import Logo from '../assets/images/Svg/logo1.svg';
import Toast from 'react-native-toast-message';
import { MaterialIcons } from '@expo/vector-icons';
import { moderateScale, scale } from 'react-native-size-matters';
import { ThemeProvider } from '../components/ThemeProvider';
import { DarkTheme, DefaultTheme } from '@react-navigation/native';
import FontProvider from '../components/FontProvider';
import { configurePushNotifications } from '../services/pushNotifications';
// import NavigationLoader from '../components/common/NavigationLoader';
import * as Sentry from '@sentry/react-native';
import { useNavigationGuards } from '../hooks/useNavigationGuards';
import { useSafeNavigate } from '../hooks/useSafeNavigate';

Sentry.init({
  dsn: 'https://<EMAIL>/4509498887372880',

  sendDefaultPii: true,

  tracesSampleRate: 1.0,

  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1,
  integrations: [Sentry.mobileReplayIntegration(), Sentry.feedbackIntegration()],

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

configurePushNotifications();


SplashScreen.preventAutoHideAsync();

export default Sentry.wrap(function RootLayout() {
  const authState = useAuth();
  const { isInitialized, user, session } = authState;
  const { profile } = useUserStore();
  const fadeAnim = React.useRef(new Animated.Value(0.3)).current;
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const segments = useSegments();
  const pathname = usePathname();
  const navigation = useNavigation();
  
  
  const isMountedRef = useRef(true);
  const [forceSplashHide, setForceSplashHide] = useState(false);
  const splashTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const fadeAnimationRef = useRef<Animated.CompositeAnimation | null>(null);
  const [fontLoadingComplete, setFontLoadingComplete] = useState(false);
  const [fontLoadingSuccess, setFontLoadingSuccess] = useState(false);

  // Integrate new navigation hooks
  const { safeNavigate, isNavigating } = useSafeNavigate();
  useNavigationGuards(fontLoadingComplete, !authState.isLoading && authState.isInitialized); // Pass appReady status

  const { LightTheme, DarkTheme: PaperDarkTheme } = adaptNavigationTheme({
    reactNavigationLight: DefaultTheme,
    reactNavigationDark: DarkTheme,
  });

  const CombinedLightTheme = {
    ...MD3LightTheme,
    ...LightTheme,
    colors: {
      ...MD3LightTheme.colors,
      ...LightTheme.colors,
      primary: '#1E8DCC',
      secondary: '#22C55E',
      background: '#FFFFFF',
      surface: '#FFFFFF',
      error: '#EF4444',
    },
    fonts: {
      ...MD3LightTheme.fonts,
      regular: {
        fontFamily: !fontLoadingSuccess ? undefined : 'Urbanist-Regular',
        fontWeight: 'normal',
      },
      medium: {
        fontFamily: !fontLoadingSuccess ? undefined : 'Urbanist-Medium',
        fontWeight: 'normal',
      },
      bold: {
        fontFamily: !fontLoadingSuccess ? undefined : 'Urbanist-Bold',
        fontWeight: 'normal',
      },
      heavy: {
        fontFamily: !fontLoadingSuccess ? undefined : 'Urbanist-Black',
        fontWeight: 'normal',
      },
    },
  };

  const CombinedDarkTheme = {
    ...MD3DarkTheme,
    ...PaperDarkTheme,
    colors: {
      ...MD3DarkTheme.colors,
      ...PaperDarkTheme.colors,
      primary: '#33a9d4',
      secondary: '#22C55E',
      background: '#121212',
      surface: '#262626',
      error: '#EF4444',
    },
    fonts: {
      ...MD3DarkTheme.fonts,
      regular: {
        fontFamily: !fontLoadingSuccess ? undefined : 'Urbanist-Regular',
        fontWeight: 'normal',
      },
      medium: {
        fontFamily: !fontLoadingSuccess ? undefined : 'Urbanist-Medium',
        fontWeight: 'normal',
      },
      bold: {
        fontFamily: !fontLoadingSuccess ? undefined : 'Urbanist-Bold',
        fontWeight: 'normal',
      },
      heavy: {
        fontFamily: !fontLoadingSuccess ? undefined : 'Urbanist-Black',
        fontWeight: 'normal',
      },
    },
  };

  const paperTheme = isDark ? CombinedDarkTheme : CombinedLightTheme;

  const handleFontLoadingComplete = useCallback((success: boolean) => {
    if (!isMountedRef.current) return;
    setFontLoadingComplete(true);
    setFontLoadingSuccess(success);
  }, []);

  useEffect(() => {
    splashTimeoutRef.current = setTimeout(() => {
      if (!isMountedRef.current) return;
      setForceSplashHide(true);
      SplashScreen.hideAsync().catch(() => {});
    }, 6000);

    const shouldHideSplash = (fontLoadingComplete) && (!authState.isLoading || forceSplashHide);
    
    if (shouldHideSplash) {
      if (splashTimeoutRef.current) {
        clearTimeout(splashTimeoutRef.current);
        splashTimeoutRef.current = null;
      }
      
      SplashScreen.hideAsync()
        .then(() => {
          if (isMountedRef.current) {
            setTimeout(() => {
              if (isMountedRef.current) {
              }
            }, 100);
          }
        })
        .catch((error) => {
          console.error("Failed to hide splash screen:", error);
          if (isMountedRef.current) {
          }
        });
    }

    return () => {
      if (splashTimeoutRef.current) {
        clearTimeout(splashTimeoutRef.current);
        splashTimeoutRef.current = null;
      }
    };
  }, [fontLoadingComplete, authState.isLoading, forceSplashHide]);

  useEffect(() => {
    if (!authState.isLoading) return;
    
    const fadeInOut = () => {
      const sequence = Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800, 
          useNativeDriver: true,
          easing: Easing.ease
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 800, 
          useNativeDriver: true,
          easing: Easing.ease
        })
      ]);

      fadeAnimationRef.current = sequence;
      sequence.start(({ finished }) => {
        if (finished && authState.isLoading) {
          fadeInOut();
        }
      });
    };

    fadeInOut();

    return () => {
      if (fadeAnimationRef.current) {
        fadeAnimationRef.current.stop();
        fadeAnimationRef.current = null;
      }
    };
  }, [authState.isLoading]);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const onLayoutRootView = async () => {
    if (Platform.OS === 'android') {
      await new Promise(resolve => setTimeout(resolve, 300));
    }
  };

  return (
    <ThemeProvider>
      <PaperProvider theme={paperTheme}>
        <FontProvider onFontLoadingComplete={handleFontLoadingComplete}>
          <NetworkStatus />
          <Stack>
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
            <Stack.Screen name="(provider)" options={{ headerShown: false }} />
            <Stack.Screen name="(auth)" options={{ headerShown: false }} />
            <Stack.Screen name="index" options={{ headerShown: false }} />
            <Stack.Screen name="onboarding" options={{ headerShown: false }} />
            <Stack.Screen name="request" options={{ headerShown: false }} />
            <Stack.Screen name="services" options={{ headerShown: false }} />
            <Stack.Screen name="provider" options={{ headerShown: false }} />
            <Stack.Screen name="booking" options={{ headerShown: false }} />
            <Stack.Screen name="chat" options={{ headerShown: false }} />
            <Stack.Screen name="support" options={{ headerShown: false }} />
            <Stack.Screen name="payment" options={{ headerShown: false }} />
            <Stack.Screen name="transactions" options={{ headerShown: false }} />
            <Stack.Screen name="terms&condition" options={{ headerShown: false }} />
          </Stack>
          <Toast
            position="top"
            topOffset={moderateScale(55)}
          />
        </FontProvider>
      </PaperProvider>
    </ThemeProvider>
  );
});