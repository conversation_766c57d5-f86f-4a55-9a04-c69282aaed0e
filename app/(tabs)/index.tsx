import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import {
  TouchableOpacity,
  Text,
  Platform,
  BackHandler,
  Dimensions,
  InteractionManager,
  View,
  AppState,
  ToastAndroid,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useNavigation, useFocusEffect } from 'expo-router';
import { supabase } from '../../services/supabase';
import { useUserStore } from '../../store/useUserStore';
import { Provider } from '../../types';
import { Colors } from '../../constants/Colors';
import { Ionicons } from '@expo/vector-icons';
import BannerSlider from '../../components/BannerSlider';
import { ScaledSheet } from 'react-native-size-matters';
import Header from '../../components/Header';
import ProviderHomeScreen from '../../components/provider/ProviderHomeScreen';
import { ProviderList } from '../../components/user/home/<USER>';
import { ServicesSection } from '../../components/user/home/<USER>';
import { HeaderSection } from '../../components/user/home/<USER>';
import { useTheme } from '../../components/ThemeProvider';
import { Snackbar } from 'react-native-paper';
import { useLocation } from '../../hooks/useLocation';
import TermiiXHRService from '../../services/termiiXHR';

const { width } = Dimensions.get('window');
const isSmallDevice = width < 375;

const ITEMS_PER_PAGE = 20;
const MAX_PROVIDERS_DISPLAY = 10; 
const PREFETCH_TIMEOUT = 5000; 

const providerCache: {[key: string]: {data: any, timestamp: number}} = {};

const CACHE_EXPIRATION = 15 * 60 * 1000;

export default function HomeScreen() {
  const router = useRouter();
  const navigation = useNavigation();
  const { profile, updateProfile } = useUserStore();
  const { isDark, colors } = useTheme();

  const {
    location,
    locationText,
    state,
    lga,
    locationError,
    isRetrying,
    isInitialized: locationInitialized,
    retryLocation
  } = useLocation();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [prefetchedProviders, setPrefetchedProviders] = useState<{[key: string]: any}>({});
  const [loadingProviderId, setLoadingProviderId] = useState<string | null>(null);
  const appState = useRef(AppState.currentState);
  const lastActiveTime = useRef<number>(Date.now());

  const isNavigating = useRef(false);
  const isMounted = useRef(true);

  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [showPhoneVerificationPrompt, setShowPhoneVerificationPrompt] = useState(false);

  useEffect(() => {
    console.log('HomeScreen mounted. Profile ID:', profile?.id);
    if (!profile?.id) {
      router.replace('/(auth)/login');
    }
    
    if (profile && profile.phone_verified === false) {
      setShowPhoneVerificationPrompt(true);
    } else {
      setShowPhoneVerificationPrompt(false);
    }

    return () => {
      isMounted.current = false;
      console.log('HomeScreen unmounted. isMounted set to false.');
    };
  }, [profile, router]);

  useEffect(() => {
    console.log('Clearing old provider cache...');
    const now = Date.now();
    Object.keys(providerCache).forEach(key => {
      if (now - providerCache[key].timestamp > CACHE_EXPIRATION) {
        delete providerCache[key];
        console.log('Deleted expired cache for:', key);
      }
    });
  }, []);

  const filteredProviders = useMemo(() => {
    console.log('Recalculating filteredProviders. Query:', searchQuery, 'Providers count:', providers.length);
    if (!searchQuery.trim()) return providers;
    
    const query = searchQuery.toLowerCase().trim();
    const filtered = providers.filter(provider => {
      const name = provider.users?.name?.toLowerCase() || '';
      const services = provider.services.map(s => s.toLowerCase());
      
      return (
        name.includes(query) ||
        services.some(service => service.includes(query))
      );
    });
    
    return filtered.slice(0, MAX_PROVIDERS_DISPLAY);
  }, [providers, searchQuery]);

  const recommendedProviders = useMemo(() => {
    console.log('Recalculating recommendedProviders. Providers count:', providers.length);
    
    if (providers.length === 0) {
      console.log('No providers available for recommendation');
      return [];
    }
    
    const shuffled = [...providers].sort(() => 0.5 - Math.random());
    const randomProviders = shuffled.slice(0, MAX_PROVIDERS_DISPLAY);
    
    console.log('Random providers count:', randomProviders.length);
    return randomProviders;
  }, [providers]);

  const displayProviders = useMemo(() => {
    console.log('Determining displayProviders. Recommended count:', recommendedProviders.length, 'Providers count:', providers.length);
    return recommendedProviders;
  }, [recommendedProviders, providers.length]);

  useEffect(() => {
    console.log('Providers state changed. Count:', providers.length, 'Loading:', loading);
    if (providers.length > 0) {
      console.log('First provider sample:', providers[0]);
      console.log('Provider services:', providers[0].services);
      console.log('Provider calculated rating:', providers[0].calculatedRating);
    }
  }, [providers, loading]);

  useEffect(() => {
    console.log('Recommended providers changed. Count:', recommendedProviders.length);
    if (recommendedProviders.length > 0) {
      console.log('First recommended provider:', recommendedProviders[0]);
    }
  }, [recommendedProviders]);

  useEffect(() => {
    console.log('Display providers changed. Count:', displayProviders.length);
    if (displayProviders.length > 0) {
      console.log('First display provider:', displayProviders[0]);
    }
  }, [displayProviders]);

  const fetchProviders = useCallback(async () => {
    console.log('fetchProviders called. Profile ID:', profile?.id);
    if (!profile?.id) {
      console.log('No profile ID, aborting fetchProviders.');
      return;
    }
    
    try {
      if (isMounted.current) {
        setLoading(true);
        console.log('setLoading(true) in fetchProviders.');
      }
      
      const fetchTimeoutId = setTimeout(() => {
        if (isMounted.current) {
          console.log('Provider fetch operation timed out completely');
          setLoading(false);
          setProviders(prev => {
            console.log('Setting providers on timeout. Previous count:', prev.length);
            return prev.length > 0 ? prev : []; 
          }); 
          
          const message = 'Loading timed out. Pull down to retry.';
          if (Platform.OS === 'android') {
            ToastAndroid.show(message, ToastAndroid.SHORT);
          } else {
            setSnackbarMessage(message);
            setSnackbarVisible(true);
          }
        }
      }, 20000); 
      
      let data;
      try {
        console.log('Fetching providers from Supabase...');
        const { data: providerData, error: fetchError } = await supabase
          .from('providers')
          .select(`
            *,
            users:user_id (id, name, email, profile_pic)
          `)
          .eq('availability', true)
          .not('user_id', 'eq', profile.id)
          .order('created_at', { ascending: false });
        
        if (fetchError) {
          console.error('Error fetching providers data from Supabase:', fetchError);
          throw fetchError;
        }
        
        data = providerData;
        console.log('Raw providers data received (count):', data ? data.length : 0, 'Data:', data);
        
        if (!data || data.length === 0) {
          clearTimeout(fetchTimeoutId);
          if (isMounted.current) {
            setProviders([]);
            setPage(0);
            setLoading(false);
            console.log('No providers found, setting empty array and setLoading(false).');
          }
          return;
        }
      } catch (fetchError) {
        console.error('Error fetching providers data (Supabase catch):', fetchError);
        clearTimeout(fetchTimeoutId);
        
        if (isMounted.current) {
          setLoading(false);
          setProviders(prev => {
            console.log('Setting providers on fetch error. Previous count:', prev.length);
            return prev.length > 0 ? prev : [];
          });
        }
        return;
      }

      try {
        console.log('Setting up providers for display...');
        console.log('isMounted.current:', isMounted.current);
        console.log('Data length:', data.length);
        
        const basicProviders = data.map(provider => ({
          ...provider,
          calculatedRating: provider.rating || 0,
          reviews: []
        }));

        console.log('Basic providers created:', basicProviders.length);
        console.log('First provider sample:', basicProviders[0]);

        console.log('About to call setProviders with:', basicProviders.length, 'providers');
        setProviders(basicProviders);
        setPage(0);
        setLoading(false);
        console.log('setProviders called with basic data. Count:', basicProviders.length);
        console.log('setLoading(false) called immediately');
        console.log('Enhanced providers sample:', basicProviders.slice(0, 2));
        
      } catch (processingError) {
        console.error('Error processing providers:', processingError);
        if (data && data.length > 0) {
          const basicProviders = data.map(provider => ({
            ...provider,
            calculatedRating: provider.rating || 0,
            reviews: []
          }));
          setProviders(basicProviders);
          setLoading(false);
          console.log('Loading set to false after processing error');
        }
      } finally {
        clearTimeout(fetchTimeoutId);
      }
    } catch (error) {
      console.error('Error in fetchProviders:', error);
      if (isMounted.current) {
        setLoading(false);
        setProviders(prev => {
          console.log('Setting providers on unexpected error. Previous count:', prev.length);
          return prev.length > 0 ? prev : [];
        });
      }
    }
  }, []); 

  const loadMoreProviders = useCallback(async () => {
    console.log('loadMoreProviders called. Loading:', loading, 'Refreshing:', refreshing, 'SearchQuery:', searchQuery);
    if (loading || refreshing) return;
    
    if (searchQuery) {
      console.log('Skipping loadMoreProviders due to search query.');
      return;
    }
    
    const nextPage = page + 1;
    const startAfter = nextPage * ITEMS_PER_PAGE;
    
    try {
      if (isMounted.current) {
        setLoading(true);
      }
      const { data, error } = await supabase
        .from('providers')
        .select(`
          *,
          users:user_id (id, name, email, profile_pic)
        `)
        .eq('availability', true)
        .not('user_id', 'eq', profile?.id || '')
        .range(startAfter, startAfter + ITEMS_PER_PAGE - 1)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      if (data.length === 0) {
        if (isMounted.current) {
          setLoading(false);
        }
        return;
      }

      const processProviderBatch = async (providers: any[], startIdx: number, batchSize: number) => {
        const endIdx = Math.min(startIdx + batchSize, providers.length);
        const batch = providers.slice(startIdx, endIdx);
        
        return batch.map(provider => ({
          ...provider,
          calculatedRating: provider.rating || 0,
          reviews: []
        }));
      };
      
      const batchSize = 5;
      let results: Provider[] = [];
      
      const batchPromises = [];
      for (let i = 0; i < data.length; i += batchSize) {
        batchPromises.push(processProviderBatch(data, i, batchSize));
      }
      
      const batchResults = await Promise.all(batchPromises);
      
      results = batchResults.flat();

      if (isMounted.current) {
        setProviders(prev => [...prev, ...results]);
        setPage(nextPage);
        setLoading(false);
      }
    } catch (error) {
      console.error('Error loading more providers:', error);
      if (isMounted.current) {
        setLoading(false);
      }
    }
  }, [loading, refreshing, page, profile?.id, searchQuery]);

  useEffect(() => {
    console.log('Main useEffect for data initialization. Profile ID:', profile?.id);
    if (!profile?.id) return;

    console.log('Calling fetchProviders from main useEffect.');
    fetchProviders();
  }, [profile?.id, fetchProviders]); 

  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (
        (appState.current === 'background' || appState.current === 'inactive') &&
        nextAppState === 'active'
      ) {
        const now = Date.now();
        const timeInBackground = now - lastActiveTime.current;
        const fiveMinutesInMs = 5 * 60 * 1000;
        
        if (timeInBackground > fiveMinutesInMs) {
          
          if (isMounted.current) {
            setLoading(true);
          }

          InteractionManager.runAfterInteractions(() => {
            fetchProviders();
          });
        }
      }
      
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        lastActiveTime.current = Date.now();
      }
      
      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, []); 

  useFocusEffect(
    useCallback(() => {
      console.log('useFocusEffect for BackHandler added.');
      let backPressCount = 0;
      let backPressTimer: NodeJS.Timeout | null = null;

      const handleBackPress = () => {
        if (backPressCount === 1) {
          BackHandler.exitApp();
          return true;
        } else {
          backPressCount += 1;
          
          const message = 'Press back again to exit';
          
          if (Platform.OS === 'android') {
            ToastAndroid.show(message, ToastAndroid.SHORT);
          } else {
            setSnackbarMessage(message);
            setSnackbarVisible(true);
          }
          
          if (backPressTimer) clearTimeout(backPressTimer);
          backPressTimer = setTimeout(() => {
            backPressCount = 0;
          }, 2000);
          
          return true;
        }
      };

      if (Platform.OS === 'android') {
        const subscription = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
        return () => {
          subscription.remove();
          if (backPressTimer) clearTimeout(backPressTimer);
        };
      }
    }, [])
  );

  useFocusEffect(
    useCallback(() => {
      console.log('useFocusEffect for navigation cleanup.');
      isNavigating.current = false;
      
      return () => {
        isNavigating.current = false;
        if (isMounted.current) {
          setLoading(false);
          setRefreshing(false);
          console.log('useFocusEffect cleanup: Loading, Refreshing set to false.');
        }
      };
    }, [])
  );

  const prefetchProviderDetails = useCallback(async (providerId: string) => {
    console.log('prefetchProviderDetails called for:', providerId);
    const now = Date.now();
    if (providerCache[providerId] && (now - providerCache[providerId].timestamp < CACHE_EXPIRATION)) {
      console.log('Using cached provider data for:', providerId);
      setPrefetchedProviders(prev => ({
        ...prev,
        [providerId]: providerCache[providerId].data
      }));
      return;
    }
    
    if (prefetchedProviders[providerId]) return;
    
    try { 
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), PREFETCH_TIMEOUT);
      
      const { data, error } = await supabase
        .from('providers')
        .select(`
          *,
          users:user_id (id, name, email, profile_pic, phone)
        `)
        .eq('id', providerId)
        .single();
        
      clearTimeout(timeoutId);
      
      if (error) throw error;
      
      if (data && isMounted.current) {
        setPrefetchedProviders(prev => ({
          ...prev,
          [providerId]: data
        }));
        
        providerCache[providerId] = {
          data,
          timestamp: Date.now()
        };
      }
    } catch (error) {
      console.log('Prefetch failed for provider:', providerId);
    }
  }, [prefetchedProviders]);
  
  const handleProviderItemPress = useCallback((id: string) => {
    console.log('handleProviderItemPress called for:', id);
    InteractionManager.runAfterInteractions(() => {
      prefetchProviderDetails(id);
    });
  }, [prefetchProviderDetails]);

  const onRefresh = useCallback(async () => {
    console.log('onRefresh called. Refreshing state:', refreshing);
    if (refreshing) return;
    setRefreshing(true);
    console.log('setRefreshing(true) in onRefresh.');
    
    const refreshTimeoutId = setTimeout(() => {
      if (isMounted.current) {
        setRefreshing(false);
        console.log('Refresh operation timed out.');
        
        const message = 'Refresh timed out. Try again later.';
        if (Platform.OS === 'android') {
          ToastAndroid.show(message, ToastAndroid.SHORT);
        } else {
          setSnackbarMessage(message);
          setSnackbarVisible(true);
        }
      }
    }, 15000);
    
    try {
      console.log('Calling fetchProviders from onRefresh.');
      await fetchProviders();
      
      
      clearTimeout(refreshTimeoutId);
      console.log('Refresh successful.');
    } catch (error) {
      console.error('Error during refresh:', error);

      const message = 'Something went wrong. Please try again.';
      if (Platform.OS === 'android') {
        ToastAndroid.show(message, ToastAndroid.SHORT);
      } else {
        setSnackbarMessage(message);
        setSnackbarVisible(true);
      }
      
      clearTimeout(refreshTimeoutId);
    } finally {
      if (isMounted.current) {
        setRefreshing(false);
        console.log('setRefreshing(false) in onRefresh finally block.');
      }
    }
  }, [refreshing, fetchProviders]);

  const shouldRenderProviderView = profile?.role === 'provider';

  const providerView = useMemo(() => (
    console.log('Rendering ProviderHomeScreen for role: provider'),
    <ProviderHomeScreen 
      profile={profile}
      onRefresh={onRefresh}
      refreshing={refreshing}
    />
  ), [profile, onRefresh, refreshing]);


  const handleServicePress = useCallback((serviceName: string) => {
    console.log('handleServicePress called for:', serviceName);
    router.push(`/services/${serviceName}`);
  }, [router]);

  const handleSeeAllPress = useCallback(() => {
    console.log('handleSeeAllPress called.');
    navigation.navigate('services' as never);
    setTimeout(() => {
      useUserStore.setState(state => ({
        ...state,
        selectedOrderTab: 'ALL'
      }));
      console.log('Selected order tab set to ALL.');
    }, 100);
  }, [navigation]);

  const handleProviderPress = useCallback((id: string) => {
    console.log('handleProviderPress called for:', id, 'isNavigating:', isNavigating.current);
    if (isNavigating.current) {
      console.log('Navigation already in progress, ignoring tap');
      return;
    }
    
    isNavigating.current = true;
    
    setLoadingProviderId(id);
    console.log('setLoadingProviderId to:', id);
    
    setTimeout(() => {
      try {
        const providerData = prefetchedProviders[id] || providerCache[id]?.data;
        console.log('Navigating to provider details for:', id, 'Prefetched data available:', !!providerData);
        
        router.push({
          pathname: `./(provider)/${id}`,
          params: { prefetchedData: providerData ? JSON.stringify(providerData) : undefined }
        });
      } catch (error) {
        console.error('Navigation error:', error);
        isNavigating.current = false;
        setLoadingProviderId(null);
      }
    }, 10);
    
    setTimeout(() => {
      isNavigating.current = false;
      setLoadingProviderId(null);
      console.log('Navigation debounce ended. isNavigating:', false, 'loadingProviderId:', null);
    }, 1000);
    
  }, [router, prefetchedProviders]);
  
  const handleProfileUpdate = useCallback((url: string) => {
    console.log('handleProfileUpdate called with URL:', url);
    useUserStore.setState(state => ({
      profile: { ...state.profile!, profile_pic: url }
    }));
  }, []);

  const ListHeaderComponent = useMemo(() => (
    console.log('Re-rendering ListHeaderComponent. LocationText:', locationText, 'Providers count:', providers.length),
    <>
      <HeaderSection
        location={location}
        state={state}
        lga={lga}
        locationText={locationText}
        setState={() => {}}
        setLga={() => {}}
        getLocation={retryLocation}
        isRetrying={isRetrying}
        locationError={locationError}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
      />
      <BannerSlider profile={profile} />
      <ServicesSection
        onServicePress={handleServicePress}
        onSeeAllPress={handleSeeAllPress}
      />
      <Text style={[
        styles.sectionTitle, 
        { color: isDark ? colors.text : '#333' }
      ]}>
        Available Providers
      </Text>
    </>
  ), [
    location, state, lga, locationText, retryLocation, 
    isRetrying, locationError, searchQuery, profile,
    handleServicePress, handleSeeAllPress, isDark, colors.text,
    providers.length
  ]);

  const resetAppState = useCallback(async () => {
    console.log('resetAppState called.');
    if (isMounted.current) {
      setLoading(true);
      setProviders([]);
      setPrefetchedProviders({});
      setPage(0);

      Object.keys(providerCache).forEach(key => {
        delete providerCache[key];
      });
      console.log('App state variables reset. Cache cleared.');
      
      setTimeout(() => {
        if (isMounted.current) {
          const message = 'App state reset';
          
          if (Platform.OS === 'android') {
            ToastAndroid.show(message, ToastAndroid.SHORT);
          } else {
            setSnackbarMessage(message);
            setSnackbarVisible(true);
          }
          
          fetchProviders();
        }
      }, 300);
    }
  }, []);

  return (
    <SafeAreaView style={[
      styles.container, 
      { backgroundColor: isDark ? '#000' : '#f9f9f9' }
    ]}>
      {!profile ? (
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          {/* transient state during navigation */}
          <Text style={{color: isDark ? '#fff' : '#000'}}>Loading profile...</Text>
        </View>
      ) : shouldRenderProviderView ? (
        providerView
      ) : (
        <>
          <Header 
            profile={profile}
            onUpdateProfilePic={handleProfileUpdate}
            onReset={resetAppState}
          />
          {!profile?.profile_pic && (
            <TouchableOpacity 
              style={[
                styles.profilePrompt, 
                { 
                  backgroundColor: isDark ? 'rgba(51,169,212,0.2)' : 'rgba(28,126,222,0.1)' 
                }
              ]}
              onPress={() => router.push('/profile')}
            >
              <Ionicons 
                name="person-add-outline" 
                size={isSmallDevice ? 20 : 24} 
                color={isDark ? colors.tint : Colors.primary} 
              />
              <Text style={[
                styles.promptText, 
                { color: isDark ? colors.tint : Colors.primary }
              ]}>
                Complete your profile by adding a photo
              </Text>
              <Ionicons 
                name="chevron-forward" 
                size={isSmallDevice ? 20 : 24} 
                color={isDark ? colors.tint : Colors.primary} 
              />
            </TouchableOpacity>
          )}

      {showPhoneVerificationPrompt && profile?.phone_verified === false && (
        <TouchableOpacity
          style={[
            styles.phoneVerificationPrompt,
            {
              backgroundColor: isDark ? 'rgba(255,165,0,0.2)' : 'rgba(255,165,0,0.1)',
            }
          ]}
          onPress={async () => {
            if (profile?.phone && profile?.id) {
              try {
                const otpResult = await TermiiXHRService.sendOTP(profile.id, profile.phone);
                if (otpResult.success && otpResult.referenceId) {
                  router.push({
                    pathname: "/(auth)/verify-otp",
                    params: {
                      phone: profile.phone,
                      userId: profile.id,
                      referenceId: otpResult.referenceId
                    }
                  });
                } else {
                  setSnackbarMessage('Failed to send OTP. Please try again.');
                  setSnackbarVisible(true);
                }
              } catch (error: any) {
                console.error('Error sending OTP from home screen prompt:', error);
                setSnackbarMessage(error.message || 'An unexpected error occurred while sending OTP.');
                setSnackbarVisible(true);
              }
            } else {
              setSnackbarMessage('Phone number or User ID is missing.');
              setSnackbarVisible(true);
            }
          }}
        >
          <Ionicons
            name="information-circle-outline"
            size={isSmallDevice ? 20 : 24}
            color={isDark ? Colors.light.accent : Colors.light.orangeWarning}
          />
          <Text
            style={[
              styles.promptText,
              { color: isDark ? Colors.light.accent : Colors.light.orangeWarning }
            ]}
          >
            Please verify your phone number in your profile settings.
          </Text>
          <Ionicons
            name="chevron-forward"
            size={isSmallDevice ? 20 : 24}
            color={isDark ? Colors.light.accent : Colors.light.orangeWarning}
          />
        </TouchableOpacity>
      )}
      
      <ProviderList
        providers={searchQuery ? filteredProviders : displayProviders}
        loading={loading}
        refreshing={refreshing}
        onRefresh={onRefresh}
        onLoadMore={loadMoreProviders}
        onProviderPress={handleProviderPress}
        searchQuery={searchQuery}
        ListHeaderComponent={ListHeaderComponent}
        isDark={isDark}
        themeColors={colors}
        loadingProviderId={loadingProviderId}
      />
        </>
      )}
      <Snackbar
        visible={snackbarVisible && Platform.OS !== 'android'}
        onDismiss={() => setSnackbarVisible(false)}
        duration={2000}
        action={{
          label: 'OK',
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
}

const styles = ScaledSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9f9f9',
  },
  profilePrompt: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(28,126,222,0.1)',
    padding: isSmallDevice ? '8@ms' : '12@ms',
    marginHorizontal: isSmallDevice ? '12@ms' : '16@ms',
    borderRadius: 8,
    marginTop: isSmallDevice ? '6@ms' : '8@ms',
  },
  promptText: {
    color: Colors.primary,
    fontFamily: 'Urbanist-Medium',
    marginHorizontal: isSmallDevice ? '6@ms' : '8@ms',
    fontSize: isSmallDevice ? '12@ms' : '14@ms',
  },
  sectionTitle: {
    fontSize: isSmallDevice ? '16@ms' : '18@ms',
    fontFamily: 'Urbanist-Bold',
    color: '#333',
    marginBottom: isSmallDevice ? '12@ms' : '16@ms',
    paddingHorizontal: isSmallDevice ? '12@ms' : '16@ms',
    marginTop: isSmallDevice ? '12@ms' : '16@ms',
  },
  phoneVerificationPrompt: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255,165,0,0.1)',
    padding: isSmallDevice ? '8@ms' : '12@ms',
    marginHorizontal: isSmallDevice ? '12@ms' : '16@ms',
    borderRadius: 8,
    marginTop: isSmallDevice ? '6@ms' : '8@ms',
    borderWidth: 1,
    borderColor: 'rgba(255,165,0,0.3)',
  },
});