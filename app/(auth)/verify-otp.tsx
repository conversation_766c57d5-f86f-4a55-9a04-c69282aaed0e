import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  StatusBar,
  ScrollView,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Dimensions,
  useColorScheme,
} from 'react-native';
import { Text } from 'react-native-paper';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from '../../constants/Colors';
import { AntDesign } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';
import Logo from '../../assets/images/Svg/logo1.svg';
import Toast from 'react-native-toast-message';
import { createClient } from '@supabase/supabase-js';
import TermiiXHRService from '../../services/termiiXHR';
import { useUserStore } from '../../store/useUserStore';

const { width, height } = Dimensions.get('window');

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseKey);

const add_phone_number_verification_status = async (userId: string, phoneNumber: string, referenceId: string) => {
  try {
    const { data, error } = await supabase
      .from('phone_number_verification_status')
      .insert([
        { user_id: userId, phone_number: phoneNumber, reference_id: referenceId, verified: false }
      ]);
    if (error) {
      console.error('Error inserting phone verification status:', error);
      return null;
    }
    return data;
  } catch (err) {
    console.error('Unexpected error in add_phone_number_verification_status:', err);
    return null;
  }
};

export default function VerifyOTP() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const phone = params.phone as string;
  const userId = params.userId as string;
  const referenceId = params.referenceId as string;
  const colorScheme = useColorScheme();
  const { refreshProfile } = useUserStore();
  
  const [otp, setOtp] = useState(['', '', '', '']);
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(20);
  const [resendDisabled, setResendDisabled] = useState(true);
  const [phoneError, setPhoneError] = useState<string | null>(null);
  
  
  const inputRefs = useRef<Array<TextInput | null>>([null, null, null, null]);

  const primaryColor = colorScheme === 'dark' ? Colors.dark.tint : Colors.light.tint;
  const backgroundColor = colorScheme === 'dark' ? Colors.dark.background : Colors.light.background;
  const textColor = colorScheme === 'dark' ? Colors.dark.text : Colors.light.text;
  const cardColor = colorScheme === 'dark' ? Colors.dark.cardBackground : Colors.light.cardBackground;

  useEffect(() => {
    if (!phone) {
      console.error('Missing phone number in parameters');
      setPhoneError('Missing phone number. Please go back and try again.');
    } else if (!userId) {
      console.error('Missing user ID in parameters');
      setPhoneError('Missing user information. Please go back and try again.');
    } else {
      console.log(`Initialized with phone: ${phone}, userId: ${userId}, referenceId: ${referenceId || 'None'}`);
      setResendDisabled(true); 
      setCountdown(60); 
    }
  }, [phone, userId, referenceId]);


  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;
    
    if (countdown > 0 && resendDisabled) {
      interval = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else if (countdown === 0) {
      setResendDisabled(false);
    }
    
    return () => clearInterval(interval);
  }, [countdown, resendDisabled]);


  const findLatestReferenceId = async () => {
    if (referenceId) return referenceId;
    
    console.log('Attempting to find latest reference ID for user:', userId);
    
    try {
      const { data, error } = await supabase
        .from('otp_references')
        .select('reference_id, created_at')
        .eq('user_id', userId)
        .eq('verified', false)
        .order('created_at', { ascending: false })
        .limit(1);
        
      if (error && error.details !== 'The query returned no rows') {
        console.error('Error finding reference ID:', error);
        return null;
      }
      
      if (data && data.length > 0) {
        const foundReferenceId = data[0].reference_id;
        console.log('Found reference ID:', foundReferenceId);
        
        router.setParams({ referenceId: foundReferenceId });
        
        return foundReferenceId;
      }
      
      console.log('No reference ID found in database');
      return null;
    } catch (error) {
      console.error('Error in findLatestReferenceId:', error);
      return null;
    }
  };

  const handleOtpChange = (text: string, index: number) => {
    if (!/^\d*$/.test(text)) return;
    
    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);
    
    if (text.length === 1 && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }
    
  };

  const handleBackspace = (e: any, index: number) => {
    if (e.nativeEvent.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const resendOTP = async () => {
    setLoading(true);
    try {
      console.log(`Resending OTP to ${phone} for user ${userId}`);
      
      const otpResult = await TermiiXHRService.sendOTP(userId, phone);
      
      if (!otpResult.success) {
        throw new Error('Failed to send OTP');
      }
      
      if (otpResult.referenceId) {
        console.log(`New reference ID received: ${otpResult.referenceId}`);
        
        router.setParams({ 
          referenceId: otpResult.referenceId 
        });
        
        try {
          console.log('Ensuring reference ID is stored in database');
          
          const { data: existingRef, error: checkError } = await supabase
            .from('otp_references')
            .select('id')
            .eq('reference_id', otpResult.referenceId)
            .eq('user_id', userId)
            .maybeSingle(); // Changed from .single() to .maybeSingle()
            
          if (checkError) {
            console.error('Error checking existing reference ID:', checkError);
            throw checkError;
          }
            
          if (!existingRef) {
            console.log('Reference not found in database, storing it now');
            
            const { error: insertError } = await supabase
              .from('otp_references')
              .insert({
                user_id: userId,
                phone_number: phone,
                reference_id: otpResult.referenceId,
                code: '',
                expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString(),
                created_at: new Date().toISOString(),
                verified: false
              });
              
            if (insertError) {
              console.error('Error storing reference ID:', insertError);
            } else {
              console.log('Successfully stored reference ID in database');
            }
          } else {
            console.log('Reference ID already exists in database');
          }
        } catch (dbError) {
          console.error('Error updating database with reference ID:', dbError);
        }
      }
      
      // Also insert into the new phone_number_verification_status table
      await add_phone_number_verification_status(userId, phone, otpResult.referenceId);

      setCountdown(30);
      setResendDisabled(true);
      
      setOtp(['', '', '', '']);
      
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 500);
      
      Toast.show({
        type: 'success',
        text1: 'OTP Resent!',
        text2: 'A new verification code has been sent to your phone number.',
        position: 'top',
        visibilityTime: 4000,
      });
    } catch (error: any) {
      console.error('Error resending OTP:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to resend verification code. Please try again.',
        position: 'top',
        visibilityTime: 4000,
      });
    } finally {
      setLoading(false);
    }
  };

  const verifyOTPCode = async () => {
    const otpCode = otp.join('');
    
    setLoading(true);
    
    try {
      const { data: userData, error: userCheckError } = await supabase
        .from('users')
        .select('phone_verified')
        .eq('id', userId)
        .maybeSingle(); // Changed from .single() to .maybeSingle()
        
      if (userCheckError) {
        if (userCheckError.details === 'The query returned no rows') {
          console.log('User not found or phone not verified for user:', userId);
          // Continue, as this might be a new user or a user whose phone_verified is false
          // The verification process will handle the actual verification.
        } else {
          console.error('Error checking user phone verification status:', userCheckError);
          Toast.show({
            type: 'error',
            text1: 'Verification Error',
            text2: 'Could not retrieve user information. Please try again.',
            position: 'top',
            visibilityTime: 4000,
          });
          setLoading(false);
          return;
        }
      } else if (userData?.phone_verified === true) { // Adjusted condition for maybeSingle()
        console.log('Phone already verified for user:', userId);
        
        Toast.show({
          type: 'success',
          text1: 'Already Verified',
          text2: 'Your phone number is already verified. Redirecting...',
          position: 'top',
          visibilityTime: 3000,
        });
        
        setTimeout(() => router.push('/'), 1500);
        setLoading(false);
        return;
      }
      
      const verificationReferenceId = referenceId || await findLatestReferenceId();
      
      if (!verificationReferenceId) {
        console.error('No reference ID found for verification');
        Toast.show({
          type: 'error',
          text1: 'Verification Error',
          text2: 'No valid reference ID found. Please request a new code.',
          position: 'top',
          visibilityTime: 4000,
        });
        setLoading(false);
        return;
      }
      
      console.log(`Verifying OTP code for user ${userId} with code ${otpCode} and reference ${verificationReferenceId}`);
      
      const verified = await TermiiXHRService.verifyOTP(otpCode, userId, verificationReferenceId);
      
      if (!verified) {
        console.log('Verification failed');
        Toast.show({
          type: 'error',
          text1: 'Incorrect Verification Code',
          text2: 'The code you entered is incorrect or has expired. Please try again or request a new code.',
          position: 'top',
          visibilityTime: 5000,
        });
        setLoading(false);
        return;
      }
      
      console.log('Verification successful');

      try {
        console.log('Updating otp_references table directly with verified status for reference:', verificationReferenceId);
        const { error: directUpdateRefError } = await supabase
          .from('otp_references')
          .update({ verified: true })
          .eq('user_id', userId)
          .eq('reference_id', verificationReferenceId);

        if (directUpdateRefError) {
          console.error('Error directly updating otp_references:', directUpdateRefError);
          Toast.show({
            type: 'error',
            text1: 'Verification Failed',
            text2: 'Could not update OTP reference. Please contact support.',
            position: 'top',
            visibilityTime: 4000,
          });
          setLoading(false);
          return;
        }
        console.log('Successfully updated otp_references table directly.');

        // Call the new RPC to set phone_verified to true
        console.log('Calling Supabase RPC set_phone_verified_status with user_id:', userId);
        const { data: phoneVerifiedResult, error: phoneVerifiedError } = await supabase.rpc('set_phone_verified_status', {
          p_user_id: userId,
          p_is_verified: true
        });

        if (phoneVerifiedError) {
          console.error('Error updating phone_verified status via RPC:', phoneVerifiedError);
          Toast.show({
            type: 'error',
            text1: 'Verification Failed',
            text2: 'Could not update user\'s phone verification status. Please contact support.',
            position: 'top',
            visibilityTime: 4000,
          });
          setLoading(false);
          return;
        }

        console.log('User phone_verified status updated via RPC successfully:', phoneVerifiedResult);
        
        try {
          console.log('Refreshing user profile');
          await refreshProfile();

          // No need for delayed refresh here, as the direct RPC update is authoritative.
        } catch (refreshError) {
          console.error('Error refreshing profile after phone verification:', refreshError);
        }
      } catch (updateError) {
        console.error('Exception during phone verification status update:', updateError);
      }
      
      Toast.show({
        type: 'success',
        text1: 'Verification Successful',
        text2: 'Your phone number has been verified successfully!',
        position: 'top',
        visibilityTime: 3000,
      });
      
      
      setTimeout(() => router.replace('/'), 1500);

    } catch (error: any) {
      console.error('Error during verification:', error);
      Toast.show({
        type: 'error',
        text1: 'Verification Error',
        text2: error.message || 'An unexpected error occurred. Please try again.',
        position: 'top',
        visibilityTime: 4000,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: primaryColor }]}>
      <StatusBar backgroundColor={primaryColor} barStyle={colorScheme === 'dark' ? "light-content" : "dark-content"} />
      
      <TouchableOpacity
        style={[styles.backButton, { top: 20 + (Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0) }]} // Adjust top for Android status bar
        onPress={() => router.back()}
      >
        <AntDesign name="arrowleft" size={24} color={colorScheme === 'dark' ? "white" : "white"} />
      </TouchableOpacity>
      
      <View style={styles.header}>
        <View style={[styles.logoContainer, { backgroundColor: colorScheme === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'white' }]}>
          <Logo width={80} height={80} style={styles.logo} />
        </View>
        <Animated.Text
          entering={FadeInDown.duration(800).springify()}
          style={[styles.title, { color: colorScheme === 'dark' ? "white" : "white", marginTop: 15 }]}
        >
          Verify Your Phone
        </Animated.Text>
        <Text style={[styles.subtitle, { color: colorScheme === 'dark' ? "rgba(255, 255, 255, 0.8)" : "rgba(255, 255, 255, 0.8)" }]}>
          {phoneError ? 
            phoneError : 
            `We've sent a 4-digit verification code to ${phone || 'your phone'}`
          }
        </Text>
      </View>
      
      {phoneError ? (
        <View style={[styles.errorContainer, { backgroundColor: cardColor }]}>
          <TouchableOpacity
            style={[styles.goBackButton, { backgroundColor: primaryColor }]}
            onPress={() => router.back()}
          >
            <Text style={[styles.goBackButtonText, { color: colorScheme === 'dark' ? "white" : "white" }]}>Go Back</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={[styles.formContainer, { backgroundColor: cardColor }]}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          >
            <View style={styles.otpContainer}>
              {otp.map((digit, index) => (
                <TextInput
                  key={index}
                  ref={(ref) => {
                    inputRefs.current[index] = ref;
                  }}
                  style={[
                    styles.otpInput,
                    { 
                      color: textColor,
                      backgroundColor: colorScheme === 'dark' ? Colors.dark.background : Colors.light.background,
                      borderColor: digit ? primaryColor : colorScheme === 'dark' ? '#444' : '#ddd'
                    },
                    digit ? styles.otpInputFilled : {}
                  ]}
                  value={digit}
                  onChangeText={(text) => handleOtpChange(text, index)}
                  onKeyPress={(e) => handleBackspace(e, index)}
                  keyboardType="number-pad"
                  maxLength={1}
                  autoFocus={index === 0}
                />
              ))}
            </View>

            <TouchableOpacity 
              style={[styles.resendButton, resendDisabled && styles.resendButtonDisabled]} 
              onPress={resendOTP}
              disabled={resendDisabled}
            >
              <Text style={[
                styles.resendText, 
                { color: resendDisabled ? (colorScheme === 'dark' ? '#666' : '#999') : primaryColor }
              ]}>
                {resendDisabled ? `Resend code (${countdown}s)` : 'Resend code'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.verifyButton, { backgroundColor: primaryColor }, loading && styles.loadingButton]}
              onPress={verifyOTPCode}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={styles.verifyButtonText}>Verify & Continue</Text>
              )}
            </TouchableOpacity>
          </KeyboardAvoidingView>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    position: 'absolute',
    top: 20,
    left: 20,
    zIndex: 10,
  },
  header: {
    height: '50%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
  },
  logo: {
    marginBottom: 0,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Urbanist-Bold',
    marginBottom: 15,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Urbanist-Medium',
    marginBottom: 5,
    textAlign: 'center',
    maxWidth: '80%',
  },
  subText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Medium',
    marginTop: 10,
    textAlign: 'center',
    maxWidth: '80%',
  },
  formContainer: {
    flex: 1,
    borderTopLeftRadius: 35,
    borderTopRightRadius: 35,
    paddingHorizontal: 25,
    paddingVertical: 35,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -3,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 5,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    paddingHorizontal: 10,
    marginBottom: 40,
  },
  otpInput: {
    width: 60,
    height: 70,
    borderWidth: 1.5,
    borderRadius: 18,
    fontSize: 26,
    fontFamily: 'Urbanist-Bold',
    textAlign: 'center',
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  otpInputFilled: {
    backgroundColor: 'rgba(0, 132, 255, 0.05)',
  },
  resendButton: {
    alignSelf: 'center',
    marginBottom: 25,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 10,
  },
  resendButtonDisabled: {
    opacity: 0.5,
  },
  resendText: {
    fontSize: 15,
    fontFamily: 'Urbanist-Medium',
  },
  verifyButton: {
    height: 65,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 25,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 4,
  },
  loadingButton: {
    opacity: 0.7,
  },
  verifyButtonText: {
    color: 'white',
    fontSize: 17,
    fontFamily: 'Urbanist-Bold',
  },
  errorContainer: {
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: 30,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 300,
  },
  goBackButton: {
    paddingHorizontal: 40,
    paddingVertical: 15,
    borderRadius: 16,
    marginTop: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  goBackButtonText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
  },
}); 