import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  StatusBar,
  TouchableOpacity,
  useColorScheme,
} from 'react-native';
import { Text, Button } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Colors } from '../../constants/Colors';
import { AntDesign } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';
import Logo from '../../assets/images/Svg/logo1.svg';
import { useTheme } from '../../components/ThemeProvider';

export default function VerifyEmailScreen() {
  const router = useRouter();
  const { isDark, colors } = useTheme();

  const handleGoToLogin = () => {
    router.replace('/(auth)/login');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar backgroundColor={colors.background} barStyle={isDark ? "light-content" : "dark-content"} />
      
      <View style={styles.header}>
        <View style={[styles.logoContainer, { backgroundColor: isDark ? colors.cardBackground : 'white' }]}>
          <Logo width={60} height={60} style={styles.logo} />
        </View>
        <Animated.Text
          entering={FadeInDown.duration(800).springify()}
          style={[styles.title, { color: colors.text }]}
        >
          Verify Your Email
        </Animated.Text>
        <Text style={[styles.subtitle, { color: colors.subtext }]}>
          A verification link has been sent to your email address. Please click the link to verify your account.
        </Text>
        <Text style={[styles.instructionText, { color: colors.subtext }]}>
          (If you don't see it, check your spam folder.)
        </Text>
      </View>
      
      <View style={[styles.formContainer, { backgroundColor: colors.cardBackground }]}>
        <Button 
          mode="contained"
          onPress={handleGoToLogin}
          style={[styles.loginButton, { backgroundColor: colors.tint }]}
          labelStyle={styles.loginButtonText}
        >
          Go to Login
        </Button>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
  },
  logo: {
    marginBottom: 0,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Urbanist-Bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Urbanist-Medium',
    marginBottom: 10,
    textAlign: 'center',
    maxWidth: '80%',
  },
  instructionText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Regular',
    textAlign: 'center',
    maxWidth: '80%',
    marginTop: 5,
  },
  formContainer: {
    height: '30%',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: 24,
    paddingTop: 40,
    alignItems: 'center',
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -3,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 5,
  },
  loginButton: {
    width: '80%',
    height: 60,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 4,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 17,
    fontFamily: 'Urbanist-Bold',
  },
}); 