import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://njkllbogrrqwxxgmsmyr.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5qa2xsYm9ncnJxd3h4Z21zeW15ciIsInJlZ2lvbl91cmwiOiJucmtnbGJvZ3JycXd4eGdrc215cmEiLCJpYXQiOjE3MjIwMjYxNjgsImV4cCI6MjAzNzYwMjE2OH0.00000000000000000000000000000000000000000000000000';

export const supabase = createClient(supabaseUrl, supabaseKey);

export type { AuthChangeEvent, Session, User } from '@supabase/supabase-js'; 