import { create } from 'zustand';
import { supabase } from '../services/supabase';

interface NotificationState {
    hasNewRequests: boolean;
  hasAcceptedBookings: boolean;
  
  checkNewRequests: (providerId: string) => Promise<void>;
  checkAcceptedBookings: (userId: string) => Promise<void>;
  
  clearNewRequestsNotification: () => void;
  clearAcceptedBookingsNotification: () => void;
}

export const useNotificationStore = create<NotificationState>((set, get) => ({
  hasNewRequests: false,
  hasAcceptedBookings: false,
  
  checkNewRequests: async (providerId: string) => {
    try {
      const { data, error, count } = await supabase
        .from('bookings')
        .select('id', { count: 'exact' })
        .eq('provider_id', providerId)
        .eq('status', 'pending')
        .eq('is_viewed', false);
        
      if (error) throw error;
      
      set({ hasNewRequests: (count !== null && count > 0) });
    } catch (error) {
      console.error('Error checking for new requests:', error);
    }
  },
  
  checkAcceptedBookings: async (userId: string) => {
    try {
      const { data, error, count } = await supabase
        .from('bookings')
        .select('id', { count: 'exact' })
        .eq('user_id', userId)
        .eq('status', 'accepted')
        .eq('is_viewed', false);
        
      if (error) throw error;
      
      set({ hasAcceptedBookings: (count !== null && count > 0) });
    } catch (error) {
      console.error('Error checking for accepted bookings:', error);
    }
  },
  
  clearNewRequestsNotification: () => {
    set({ hasNewRequests: false });
  },
  
  clearAcceptedBookingsNotification: () => {
    set({ hasAcceptedBookings: false });
  }
})); 